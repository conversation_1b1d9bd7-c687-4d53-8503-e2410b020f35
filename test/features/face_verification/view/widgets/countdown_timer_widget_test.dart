import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:bloomg_flutter/features/face_verification/view/widgets/countdown_timer_widget.dart';

void main() {
  group('CountdownTimerWidget', () {
    testWidgets('displays countdown number correctly',
        (WidgetTester tester) async {
      // Arrange
      const countdownValue = 3;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      expect(find.text('3'), findsOneWidget);
      expect(find.byType(Container), findsOneWidget);
    });

    testWidgets('displays different countdown values',
        (WidgetTester tester) async {
      // Test countdown 2
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: 2),
          ),
        ),
      );

      expect(find.text('2'), findsOneWidget);

      // Test countdown 1
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: 1),
          ),
        ),
      );

      expect(find.text('1'), findsOneWidget);
    });

    testWidgets('has correct styling and layout', (WidgetTester tester) async {
      // Arrange
      const countdownValue = 3;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final container = tester.widget<Container>(containerFinder);
      expect(container.decoration, isA<BoxDecoration>());

      final textFinder = find.text('3');
      expect(textFinder, findsOneWidget);

      final text = tester.widget<Text>(textFinder);
      expect(text.style?.fontSize, equals(72.0));
      expect(text.style?.fontWeight, equals(FontWeight.bold));
    });

    testWidgets('is centered on screen', (WidgetTester tester) async {
      // Arrange
      const countdownValue = 2;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      final centerFinder = find.byType(Center);
      expect(centerFinder, findsOneWidget);

      final containerFinder = find.descendant(
        of: centerFinder,
        matching: find.byType(Container),
      );
      expect(containerFinder, findsOneWidget);
    });

    testWidgets('handles zero countdown', (WidgetTester tester) async {
      // Arrange
      const countdownValue = 0;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      expect(find.text('0'), findsOneWidget);
    });

    testWidgets('handles negative countdown gracefully',
        (WidgetTester tester) async {
      // Arrange
      const countdownValue = -1;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      expect(find.text('-1'), findsOneWidget);
    });

    testWidgets('has proper accessibility semantics',
        (WidgetTester tester) async {
      // Arrange
      const countdownValue = 3;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      final semanticsFinder = find.byType(Semantics);
      expect(semanticsFinder, findsOneWidget);

      final semantics = tester.widget<Semantics>(semanticsFinder);
      expect(
        semantics.properties.label,
        equals('Recording starts in 3 seconds'),
      );
      expect(semantics.properties.liveRegion, isTrue);
    });

    testWidgets('updates accessibility label for different countdown values',
        (WidgetTester tester) async {
      // Test countdown 1
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: 1),
          ),
        ),
      );

      final semantics1 = tester.widget<Semantics>(find.byType(Semantics));
      expect(
        semantics1.properties.label,
        equals('Recording starts in 1 second'),
      );

      // Test countdown 2
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: 2),
          ),
        ),
      );

      final semantics2 = tester.widget<Semantics>(find.byType(Semantics));
      expect(
        semantics2.properties.label,
        equals('Recording starts in 2 seconds'),
      );
    });

    testWidgets('animation properties are correct',
        (WidgetTester tester) async {
      // Arrange
      const countdownValue = 3;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      final animatedScaleFinder = find.byType(AnimatedScale);
      expect(animatedScaleFinder, findsOneWidget);

      final animatedScale = tester.widget<AnimatedScale>(animatedScaleFinder);
      expect(animatedScale.duration, equals(const Duration(milliseconds: 300)));
      expect(animatedScale.scale, equals(1.0));
    });

    testWidgets('container has correct size constraints',
        (WidgetTester tester) async {
      // Arrange
      const countdownValue = 3;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CountdownTimerWidget(remainingSeconds: countdownValue),
          ),
        ),
      );

      // Assert
      final containerFinder = find.byType(Container);
      final container = tester.widget<Container>(containerFinder);

      expect(container.constraints?.minWidth, equals(120.0));
      expect(container.constraints?.minHeight, equals(120.0));
    });
  });
}
