import 'package:mockito/mockito.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:camera/camera.dart';

import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';

/// Mock implementation of FaceDetectionService for testing
class MockFaceDetectionService extends Mock implements FaceDetectionService {
  bool _isInitialized = false;
  bool _shouldDetectFace = true;
  double _coveragePercentage = 85.0;
  int _faceCount = 1;

  /// Configure mock behavior for testing scenarios
  void configureMock({
    bool shouldDetectFace = true,
    double coveragePercentage = 85.0,
    int faceCount = 1,
  }) {
    _shouldDetectFace = shouldDetectFace;
    _coveragePercentage = coveragePercentage;
    _faceCount = faceCount;
  }

  @override
  Future<void> initialize() async {
    if (!_isInitialized) {
      _isInitialized = true;
      return;
    }
    throw Exception('Service already initialized');
  }

  @override
  Future<FaceDetectionResult> processImage(InputImage inputImage) async {
    if (!_isInitialized) {
      throw Exception('Service not initialized');
    }

    // Simulate processing delay
    await Future.delayed(const Duration(milliseconds: 50));

    if (_shouldDetectFace) {
      return FaceDetectionResult(
        faceDetected: true,
        faceCount: _faceCount,
        coveragePercentage: _coveragePercentage,
        boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
        timestamp: DateTime.now(),
      );
    } else {
      return FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0.0,
        boundingBox: null,
        timestamp: DateTime.now(),
      );
    }
  }

  @override
  Future<FaceDetectionResult> processFrame(CameraImage image) async {
    // Convert CameraImage to InputImage (simplified for testing)
    final inputImage = InputImage.fromBytes(
      bytes: Uint8List(0),
      metadata: const InputImageMetadata(
        size: Size(640, 480),
        rotation: InputImageRotation.rotation0deg,
        format: InputImageFormat.nv21,
        bytesPerRow: 640,
      ),
    );
    
    return processImage(inputImage);
  }

  @override
  bool isGoodCoverage(double coveragePercentage) {
    return coveragePercentage >= 80.0;
  }

  @override
  void dispose() {
    _isInitialized = false;
  }

  @override
  bool get isInitialized => _isInitialized;
}

/// Mock implementation for testing camera failures
class MockFailingFaceDetectionService extends MockFaceDetectionService {
  @override
  Future<void> initialize() async {
    throw Exception('Camera initialization failed');
  }

  @override
  Future<FaceDetectionResult> processImage(InputImage inputImage) async {
    throw Exception('Face detection processing failed');
  }

  @override
  Future<FaceDetectionResult> processFrame(CameraImage image) async {
    throw Exception('Frame processing failed');
  }
}

/// Mock implementation for testing no face scenarios
class MockNoFaceDetectionService extends MockFaceDetectionService {
  @override
  Future<FaceDetectionResult> processImage(InputImage inputImage) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    return FaceDetectionResult(
      faceDetected: false,
      faceCount: 0,
      coveragePercentage: 0.0,
      boundingBox: null,
      timestamp: DateTime.now(),
    );
  }
}

/// Mock implementation for testing poor coverage scenarios
class MockPoorCoverageFaceDetectionService extends MockFaceDetectionService {
  @override
  Future<FaceDetectionResult> processImage(InputImage inputImage) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    return FaceDetectionResult(
      faceDetected: true,
      faceCount: 1,
      coveragePercentage: 60.0, // Below 80% threshold
      boundingBox: const Rect.fromLTWH(150, 150, 100, 100),
      timestamp: DateTime.now(),
    );
  }
}

/// Mock implementation for testing multiple faces scenarios
class MockMultipleFaceDetectionService extends MockFaceDetectionService {
  @override
  Future<FaceDetectionResult> processImage(InputImage inputImage) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    return FaceDetectionResult(
      faceDetected: true,
      faceCount: 2, // Multiple faces
      coveragePercentage: 75.0,
      boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
      timestamp: DateTime.now(),
    );
  }
}

/// Mock camera image for testing
class MockCameraImage extends Mock implements CameraImage {
  @override
  int get width => 640;

  @override
  int get height => 480;

  @override
  InputImageFormat get format => InputImageFormat.nv21;

  @override
  List<Plane> get planes => [
    MockPlane(),
  ];
}

/// Mock camera plane for testing
class MockPlane extends Mock implements Plane {
  @override
  Uint8List get bytes => Uint8List.fromList(List.filled(640 * 480, 0));

  @override
  int get bytesPerRow => 640;

  @override
  int get bytesPerPixel => 1;
}

/// Helper class for creating test scenarios
class FaceDetectionTestScenarios {
  static MockFaceDetectionService goodCoverage() {
    final service = MockFaceDetectionService();
    service.configureMock(
      shouldDetectFace: true,
      coveragePercentage: 85.0,
      faceCount: 1,
    );
    return service;
  }

  static MockFaceDetectionService poorCoverage() {
    final service = MockFaceDetectionService();
    service.configureMock(
      shouldDetectFace: true,
      coveragePercentage: 60.0,
      faceCount: 1,
    );
    return service;
  }

  static MockFaceDetectionService noFace() {
    final service = MockFaceDetectionService();
    service.configureMock(
      shouldDetectFace: false,
      coveragePercentage: 0.0,
      faceCount: 0,
    );
    return service;
  }

  static MockFaceDetectionService multipleFaces() {
    final service = MockFaceDetectionService();
    service.configureMock(
      shouldDetectFace: true,
      coveragePercentage: 75.0,
      faceCount: 2,
    );
    return service;
  }

  static MockFailingFaceDetectionService cameraFailure() {
    return MockFailingFaceDetectionService();
  }
}
