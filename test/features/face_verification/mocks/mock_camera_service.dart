import 'dart:async';
import 'package:mockito/mockito.dart';
import 'package:camera/camera.dart';

import 'package:bloomg_flutter/features/face_verification/services/camera_service.dart';

/// Mock implementation of CameraService for testing
class MockCameraService extends Mock implements CameraService {
  bool _isInitialized = false;
  bool _isRecording = false;
  bool _shouldFailInitialization = false;
  bool _shouldFailRecording = false;
  String _videoPath = '/mock/path/to/video.mp4';

  StreamController<CameraImage>? _frameStreamController;

  /// Configure mock behavior for testing scenarios
  void configureMock({
    bool shouldFailInitialization = false,
    bool shouldFailRecording = false,
    String videoPath = '/mock/path/to/video.mp4',
  }) {
    _shouldFailInitialization = shouldFailInitialization;
    _shouldFailRecording = shouldFailRecording;
    _videoPath = videoPath;
  }

  @override
  Future<void> initialize() async {
    if (_shouldFailInitialization) {
      throw CameraException('camera_error', 'Failed to initialize camera');
    }

    // Simulate initialization delay
    await Future.delayed(const Duration(milliseconds: 500));
    _isInitialized = true;
    _frameStreamController = StreamController<CameraImage>.broadcast();
  }

  @override
  Future<void> startVideoRecording() async {
    if (!_isInitialized) {
      throw CameraException('camera_not_initialized', 'Camera not initialized');
    }

    if (_shouldFailRecording) {
      throw CameraException('recording_error', 'Failed to start recording');
    }

    _isRecording = true;
    
    // Start emitting mock frames
    _startMockFrameStream();
  }

  @override
  Future<String> stopVideoRecording() async {
    if (!_isRecording) {
      throw CameraException('not_recording', 'Camera is not recording');
    }

    _isRecording = false;
    await _frameStreamController?.close();
    _frameStreamController = null;

    // Simulate video processing delay
    await Future.delayed(const Duration(milliseconds: 200));
    
    return _videoPath;
  }

  @override
  Stream<CameraImage> get frameStream {
    if (_frameStreamController == null) {
      return const Stream.empty();
    }
    return _frameStreamController!.stream;
  }

  @override
  bool get isInitialized => _isInitialized;

  @override
  bool get isRecording => _isRecording;

  @override
  CameraController? get controller => MockCameraController();

  @override
  void dispose() {
    _isInitialized = false;
    _isRecording = false;
    _frameStreamController?.close();
    _frameStreamController = null;
  }

  /// Start emitting mock camera frames for testing
  void _startMockFrameStream() {
    if (_frameStreamController == null) return;

    Timer.periodic(const Duration(milliseconds: 33), (timer) {
      if (!_isRecording || _frameStreamController == null) {
        timer.cancel();
        return;
      }

      final mockFrame = MockCameraImage();
      _frameStreamController!.add(mockFrame);
    });
  }

  /// Manually emit a frame for testing
  void emitMockFrame() {
    if (_frameStreamController != null && _isRecording) {
      _frameStreamController!.add(MockCameraImage());
    }
  }
}

/// Mock implementation for testing camera permission failures
class MockPermissionDeniedCameraService extends MockCameraService {
  @override
  Future<void> initialize() async {
    throw CameraException('camera_access_denied', 'Camera permission denied');
  }
}

/// Mock implementation for testing camera unavailable scenarios
class MockUnavailableCameraService extends MockCameraService {
  @override
  Future<void> initialize() async {
    throw CameraException('camera_unavailable', 'No cameras available');
  }
}

/// Mock implementation for testing recording failures
class MockRecordingFailureCameraService extends MockCameraService {
  @override
  Future<void> startVideoRecording() async {
    await super.initialize();
    throw CameraException('recording_failed', 'Failed to start video recording');
  }
}

/// Mock CameraController for testing
class MockCameraController extends Mock implements CameraController {
  @override
  CameraValue get value => const CameraValue(
    isInitialized: true,
    isRecordingVideo: false,
    isTakingPicture: false,
    isStreamingImages: false,
    isRecordingPaused: false,
    flashMode: FlashMode.off,
    exposureMode: ExposureMode.auto,
    focusMode: FocusMode.auto,
    exposurePointSupported: true,
    focusPointSupported: true,
    deviceOrientation: DeviceOrientation.portraitUp,
    lockedCaptureOrientation: null,
    recordingOrientation: null,
    isPreviewPaused: false,
    previewSize: Size(640, 480),
    aspectRatio: 4 / 3,
  );

  @override
  Future<void> initialize() async {
    // Mock initialization
  }

  @override
  Future<void> dispose() async {
    // Mock disposal
  }

  @override
  Widget buildPreview() {
    return Container(
      width: 640,
      height: 480,
      color: Colors.black,
      child: const Center(
        child: Text(
          'Mock Camera Preview',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}

/// Mock CameraImage for testing
class MockCameraImage extends Mock implements CameraImage {
  @override
  int get width => 640;

  @override
  int get height => 480;

  @override
  InputImageFormat get format => InputImageFormat.nv21;

  @override
  List<Plane> get planes => [
    MockPlane(),
  ];

  @override
  int get lensAperture => 0;

  @override
  int get sensorExposureTime => 0;

  @override
  double get sensorSensitivity => 0;
}

/// Mock Plane for testing
class MockPlane extends Mock implements Plane {
  @override
  Uint8List get bytes => Uint8List.fromList(List.filled(640 * 480, 128));

  @override
  int get bytesPerRow => 640;

  @override
  int get bytesPerPixel => 1;
}

/// Helper class for creating camera test scenarios
class CameraTestScenarios {
  static MockCameraService normal() {
    final service = MockCameraService();
    service.configureMock();
    return service;
  }

  static MockCameraService initializationFailure() {
    final service = MockCameraService();
    service.configureMock(shouldFailInitialization: true);
    return service;
  }

  static MockCameraService recordingFailure() {
    final service = MockCameraService();
    service.configureMock(shouldFailRecording: true);
    return service;
  }

  static MockPermissionDeniedCameraService permissionDenied() {
    return MockPermissionDeniedCameraService();
  }

  static MockUnavailableCameraService unavailable() {
    return MockUnavailableCameraService();
  }

  static MockRecordingFailureCameraService recordingFailure2() {
    return MockRecordingFailureCameraService();
  }
}

/// Test utilities for camera testing
class CameraTestUtils {
  /// Create a mock camera description for testing
  static CameraDescription createMockCameraDescription() {
    return const CameraDescription(
      name: 'Mock Front Camera',
      lensDirection: CameraLensDirection.front,
      sensorOrientation: 90,
    );
  }

  /// Create mock available cameras list
  static List<CameraDescription> createMockAvailableCameras() {
    return [
      createMockCameraDescription(),
      const CameraDescription(
        name: 'Mock Back Camera',
        lensDirection: CameraLensDirection.back,
        sensorOrientation: 90,
      ),
    ];
  }

  /// Simulate camera permission check
  static Future<bool> mockCheckCameraPermission({bool granted = true}) async {
    await Future.delayed(const Duration(milliseconds: 100));
    return granted;
  }

  /// Simulate camera permission request
  static Future<bool> mockRequestCameraPermission({bool granted = true}) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return granted;
  }
}
