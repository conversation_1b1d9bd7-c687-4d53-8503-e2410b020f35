import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_event.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_state.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/core/services/logger_service.dart';

import 'face_video_capture_bloc_test.mocks.dart';

@GenerateMocks([
  FaceDetectionRepository,
  VideoStorageRepository,
  LoggerService,
])
void main() {
  group('FaceVideoCaptureBloc', () {
    late FaceVideoCaptureBloc bloc;
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late MockVideoStorageRepository mockVideoStorageRepository;
    late MockLoggerService mockLoggerService;

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoStorageRepository = MockVideoStorageRepository();
      mockLoggerService = MockLoggerService();
      
      bloc = FaceVideoCaptureBloc(
        faceDetectionRepository: mockFaceDetectionRepository,
        videoStorageRepository: mockVideoStorageRepository,
        logger: mockLoggerService,
      );
    });

    tearDown(() {
      bloc.close();
    });

    test('initial state is FaceVideoCaptureInitial', () {
      expect(bloc.state, equals(const FaceVideoCaptureInitial()));
    });

    group('InitializeCamera', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'emits [CameraInitializing, CameraReady] when initialization succeeds',
        build: () {
          when(mockFaceDetectionRepository.initialize())
              .thenAnswer((_) async => {});
          return bloc;
        },
        act: (bloc) => bloc.add(const InitializeCamera()),
        expect: () => [
          const FaceVideoCaptureCameraInitializing(),
          const FaceVideoCaptureCameraReady(),
        ],
        verify: (_) {
          verify(mockFaceDetectionRepository.initialize()).called(1);
          verify(mockLoggerService.info('[FACE_VERIFICATION] Camera initialized successfully')).called(1);
        },
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'emits [CameraInitializing, Error] when initialization fails',
        build: () {
          when(mockFaceDetectionRepository.initialize())
              .thenThrow(Exception('Camera initialization failed'));
          return bloc;
        },
        act: (bloc) => bloc.add(const InitializeCamera()),
        expect: () => [
          const FaceVideoCaptureCameraInitializing(),
          const FaceVideoCaptureError('Failed to initialize camera: Exception: Camera initialization failed'),
        ],
        verify: (_) {
          verify(mockLoggerService.error('[FACE_VERIFICATION] Camera initialization failed: Exception: Camera initialization failed')).called(1);
        },
      );
    });

    group('StartCountdown', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'emits countdown states and then starts recording',
        build: () => bloc,
        seed: () => const FaceVideoCaptureCameraReady(),
        act: (bloc) => bloc.add(const StartCountdown()),
        expect: () => [
          const FaceVideoCaptureCountdownInProgress(3),
          const FaceVideoCaptureCountdownInProgress(2),
          const FaceVideoCaptureCountdownInProgress(1),
          const FaceVideoCaptureRecording(0),
        ],
        wait: const Duration(seconds: 4),
        verify: (_) {
          verify(mockLoggerService.info('[FACE_VERIFICATION] Countdown started')).called(1);
          verify(mockLoggerService.info('[FACE_VERIFICATION] Recording started')).called(1);
        },
      );
    });

    group('ProcessFrame', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'updates recording state with face detection result',
        build: () {
          final faceResult = FaceDetectionResult(
            faceDetected: true,
            faceCount: 1,
            coveragePercentage: 85.0,
            boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
            timestamp: DateTime.now(),
          );
          when(mockFaceDetectionRepository.processFrame(any))
              .thenAnswer((_) async => faceResult);
          return bloc;
        },
        seed: () => const FaceVideoCaptureRecording(5),
        act: (bloc) => bloc.add(ProcessFrame(mockCameraImage)),
        expect: () => [
          FaceVideoCaptureRecording(
            5,
            currentFaceResult: FaceDetectionResult(
              faceDetected: true,
              faceCount: 1,
              coveragePercentage: 85.0,
              boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
              timestamp: DateTime.now(),
            ),
          ),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'handles frame processing errors gracefully',
        build: () {
          when(mockFaceDetectionRepository.processFrame(any))
              .thenThrow(Exception('Frame processing failed'));
          return bloc;
        },
        seed: () => const FaceVideoCaptureRecording(5),
        act: (bloc) => bloc.add(ProcessFrame(mockCameraImage)),
        expect: () => [],
        verify: (_) {
          verify(mockLoggerService.error('[FACE_VERIFICATION] Frame processing failed: Exception: Frame processing failed')).called(1);
        },
      );
    });

    group('StopRecording', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'emits [Processing, Success] when validation succeeds',
        build: () {
          final successStats = FaceCoverageStats(
            totalFrames: 100,
            framesWithFace: 95,
            framesWithGoodCoverage: 85,
            averageCoverage: 82.5,
            minCoverage: 75.0,
            maxCoverage: 90.0,
          );
          when(mockVideoStorageRepository.saveVideo(any))
              .thenAnswer((_) async => '/path/to/video.mp4');
          when(mockFaceDetectionRepository.validateRecording(any))
              .thenAnswer((_) async => successStats);
          return bloc;
        },
        seed: () => const FaceVideoCaptureRecording(0),
        act: (bloc) => bloc.add(const StopRecording()),
        expect: () => [
          const FaceVideoCaptureProcessing(),
          FaceVideoCaptureSuccess(
            videoPath: '/path/to/video.mp4',
            coverageStats: FaceCoverageStats(
              totalFrames: 100,
              framesWithFace: 95,
              framesWithGoodCoverage: 85,
              averageCoverage: 82.5,
              minCoverage: 75.0,
              maxCoverage: 90.0,
            ),
          ),
        ],
        verify: (_) {
          verify(mockLoggerService.info('[FACE_VERIFICATION] Recording completed successfully')).called(1);
        },
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'emits [Processing, Failure] when validation fails',
        build: () {
          final failureStats = FaceCoverageStats(
            totalFrames: 100,
            framesWithFace: 60,
            framesWithGoodCoverage: 40,
            averageCoverage: 65.0,
            minCoverage: 30.0,
            maxCoverage: 85.0,
          );
          when(mockVideoStorageRepository.saveVideo(any))
              .thenAnswer((_) async => '/path/to/video.mp4');
          when(mockFaceDetectionRepository.validateRecording(any))
              .thenAnswer((_) async => failureStats);
          return bloc;
        },
        seed: () => const FaceVideoCaptureRecording(0),
        act: (bloc) => bloc.add(const StopRecording()),
        expect: () => [
          const FaceVideoCaptureProcessing(),
          FaceVideoCaptureFailure(
            reason: 'Insufficient face coverage during recording',
            coverageStats: FaceCoverageStats(
              totalFrames: 100,
              framesWithFace: 60,
              framesWithGoodCoverage: 40,
              averageCoverage: 65.0,
              minCoverage: 30.0,
              maxCoverage: 85.0,
            ),
          ),
        ],
        verify: (_) {
          verify(mockLoggerService.warning('[FACE_VERIFICATION] Recording failed validation')).called(1);
        },
      );
    });

    group('ResetCapture', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'resets to CameraReady state',
        build: () => bloc,
        seed: () => const FaceVideoCaptureSuccess(
          videoPath: '/path/to/video.mp4',
          coverageStats: FaceCoverageStats(
            totalFrames: 100,
            framesWithFace: 95,
            framesWithGoodCoverage: 85,
            averageCoverage: 82.5,
            minCoverage: 75.0,
            maxCoverage: 90.0,
          ),
        ),
        act: (bloc) => bloc.add(const ResetCapture()),
        expect: () => [
          const FaceVideoCaptureCameraReady(),
        ],
        verify: (_) {
          verify(mockLoggerService.info('[FACE_VERIFICATION] Capture reset')).called(1);
        },
      );
    });
  });
}

// Mock camera image for testing
final mockCameraImage = MockCameraImage();

class MockCameraImage extends Mock implements CameraImage {}
