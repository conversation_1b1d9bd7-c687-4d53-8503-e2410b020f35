import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:camera/camera.dart';

import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/core/services/logger_service.dart';

import 'face_detection_service_test.mocks.dart';

@GenerateMocks([
  FaceDetector,
  LoggerService,
])
void main() {
  group('FaceDetectionService', () {
    late FaceDetectionService faceDetectionService;
    late MockFaceDetector mockFaceDetector;
    late MockLoggerService mockLoggerService;

    setUp(() {
      mockFaceDetector = MockFaceDetector();
      mockLoggerService = MockLoggerService();
      faceDetectionService = FaceDetectionService(
        faceDetector: mockFaceDetector,
        logger: mockLoggerService,
      );
    });

    tearDown(() {
      faceDetectionService.dispose();
    });

    group('initialize', () {
      test('should initialize face detector successfully', () async {
        // Act
        await faceDetectionService.initialize();

        // Assert
        verify(mockLoggerService.info('[FACE_VERIFICATION] Service initialized successfully')).called(1);
      });

      test('should handle initialization errors', () async {
        // Arrange
        when(mockFaceDetector.processImage(any))
            .thenThrow(Exception('Initialization failed'));

        // Act & Assert
        expect(
          () => faceDetectionService.initialize(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('processImage', () {
      test('should return face detected result when face is found', () async {
        // Arrange
        final mockFace = MockFace();
        when(mockFace.boundingBox).thenReturn(const Rect.fromLTWH(100, 100, 200, 200));
        when(mockFaceDetector.processImage(any))
            .thenAnswer((_) async => [mockFace]);

        final inputImage = InputImage.fromBytes(
          bytes: Uint8List(0),
          metadata: const InputImageMetadata(
            size: Size(640, 480),
            rotation: InputImageRotation.rotation0deg,
            format: InputImageFormat.nv21,
            bytesPerRow: 640,
          ),
        );

        // Act
        final result = await faceDetectionService.processImage(inputImage);

        // Assert
        expect(result.faceDetected, isTrue);
        expect(result.faceCount, equals(1));
        expect(result.coveragePercentage, greaterThan(0));
        verify(mockLoggerService.debug('[FACE_VERIFICATION] Face detected: 1 faces, coverage: ${result.coveragePercentage.toStringAsFixed(1)}%')).called(1);
      });

      test('should return no face detected when no faces found', () async {
        // Arrange
        when(mockFaceDetector.processImage(any))
            .thenAnswer((_) async => []);

        final inputImage = InputImage.fromBytes(
          bytes: Uint8List(0),
          metadata: const InputImageMetadata(
            size: Size(640, 480),
            rotation: InputImageRotation.rotation0deg,
            format: InputImageFormat.nv21,
            bytesPerRow: 640,
          ),
        );

        // Act
        final result = await faceDetectionService.processImage(inputImage);

        // Assert
        expect(result.faceDetected, isFalse);
        expect(result.faceCount, equals(0));
        expect(result.coveragePercentage, equals(0.0));
        verify(mockLoggerService.debug('[FACE_VERIFICATION] No face detected')).called(1);
      });

      test('should calculate correct coverage percentage', () async {
        // Arrange
        final mockFace = MockFace();
        // Face covering 25% of the image (200x200 in 640x480 = 40000/307200 ≈ 13%)
        when(mockFace.boundingBox).thenReturn(const Rect.fromLTWH(220, 140, 200, 200));
        when(mockFaceDetector.processImage(any))
            .thenAnswer((_) async => [mockFace]);

        final inputImage = InputImage.fromBytes(
          bytes: Uint8List(0),
          metadata: const InputImageMetadata(
            size: Size(640, 480),
            rotation: InputImageRotation.rotation0deg,
            format: InputImageFormat.nv21,
            bytesPerRow: 640,
          ),
        );

        // Act
        final result = await faceDetectionService.processImage(inputImage);

        // Assert
        expect(result.coveragePercentage, closeTo(13.0, 1.0));
      });

      test('should handle multiple faces and return largest', () async {
        // Arrange
        final mockFace1 = MockFace();
        final mockFace2 = MockFace();
        when(mockFace1.boundingBox).thenReturn(const Rect.fromLTWH(100, 100, 100, 100));
        when(mockFace2.boundingBox).thenReturn(const Rect.fromLTWH(300, 200, 200, 200));
        when(mockFaceDetector.processImage(any))
            .thenAnswer((_) async => [mockFace1, mockFace2]);

        final inputImage = InputImage.fromBytes(
          bytes: Uint8List(0),
          metadata: const InputImageMetadata(
            size: Size(640, 480),
            rotation: InputImageRotation.rotation0deg,
            format: InputImageFormat.nv21,
            bytesPerRow: 640,
          ),
        );

        // Act
        final result = await faceDetectionService.processImage(inputImage);

        // Assert
        expect(result.faceDetected, isTrue);
        expect(result.faceCount, equals(2));
        // Should use the larger face (200x200) for coverage calculation
        expect(result.coveragePercentage, greaterThan(10));
      });

      test('should handle processing errors gracefully', () async {
        // Arrange
        when(mockFaceDetector.processImage(any))
            .thenThrow(Exception('Processing failed'));

        final inputImage = InputImage.fromBytes(
          bytes: Uint8List(0),
          metadata: const InputImageMetadata(
            size: Size(640, 480),
            rotation: InputImageRotation.rotation0deg,
            format: InputImageFormat.nv21,
            bytesPerRow: 640,
          ),
        );

        // Act & Assert
        expect(
          () => faceDetectionService.processImage(inputImage),
          throwsA(isA<Exception>()),
        );
        verify(mockLoggerService.error('[FACE_VERIFICATION] Face detection failed: Exception: Processing failed')).called(1);
      });
    });

    group('isGoodCoverage', () {
      test('should return true for coverage >= 80%', () {
        // Act & Assert
        expect(faceDetectionService.isGoodCoverage(80.0), isTrue);
        expect(faceDetectionService.isGoodCoverage(85.5), isTrue);
        expect(faceDetectionService.isGoodCoverage(100.0), isTrue);
      });

      test('should return false for coverage < 80%', () {
        // Act & Assert
        expect(faceDetectionService.isGoodCoverage(79.9), isFalse);
        expect(faceDetectionService.isGoodCoverage(50.0), isFalse);
        expect(faceDetectionService.isGoodCoverage(0.0), isFalse);
      });
    });
  });
}

// Mock classes for testing
class MockFace extends Mock implements Face {}
