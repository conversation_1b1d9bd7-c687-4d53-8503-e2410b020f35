# Face Verification Video Capture - Implementation Plan

## 🎯 Objective
Implement a face verification video capture feature that records a 9-second front-camera video with real-time face detection feedback and validation, following the existing BLoC architecture patterns.

## 📋 Core Requirements Summary

### User Flow
1. Navigate to FaceVideoCapturePage
2. Camera preview with face guide overlay
3. "Start Recording" button
4. 3-2-1 countdown display
5. 9-second video recording with real-time face detection
6. Success/failure screen based on face coverage validation
7. Local video storage for future processing

### Technical Specifications
- **Camera**: Front-facing only, optimal resolution
- **Duration**: Exactly 9 seconds (auto-stop)
- **Format**: MP4 (platform default)
- **Face Detection**: Real-time with 80% coverage threshold
- **Feedback**: Green/red border based on face detection
- **Architecture**: BLoC pattern with proper state management

## 🏗️ Implementation Plan

### Phase 1: Dependencies & Setup

#### 1.1 Add Required Dependencies
```yaml
# Add to pubspec.yaml dependencies:
google_mlkit_face_detection: ^0.13.1  # Real-time face detection
camerawesome: ^2.4.0                  # Camera functionality and video recording
```

#### 1.2 Platform Permissions Setup
- **Android**: Update `android/app/src/main/AndroidManifest.xml`
  - `<uses-permission android:name="android.permission.CAMERA" />`
  - `<uses-permission android:name="android.permission.RECORD_AUDIO" />`
  - `<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />`

- **iOS**: Update `ios/Runner/Info.plist`
  - `NSCameraUsageDescription`
  - `NSMicrophoneUsageDescription`

### Phase 2: Core Architecture

#### 2.1 Directory Structure
```
lib/features/face_verification/
├── bloc/
│   ├── face_video_capture_bloc.dart
│   ├── face_video_capture_event.dart
│   └── face_video_capture_state.dart
├── models/
│   ├── face_detection_result.dart
│   ├── video_capture_config.dart
│   └── face_coverage_stats.dart
├── repository/
│   ├── face_detection_repository.dart
│   └── video_storage_repository.dart
├── services/
│   ├── face_detection_service.dart
│   ├── camera_service.dart
│   └── video_validation_service.dart
├── view/
│   ├── face_video_capture_page.dart
│   └── widgets/
│       ├── camera_preview_widget.dart
│       ├── face_guide_overlay.dart
│       ├── countdown_timer_widget.dart
│       ├── recording_feedback_widget.dart
│       └── result_screen_widget.dart
└── utils/
    ├── face_detection_utils.dart
    └── camera_utils.dart
```

#### 2.2 BLoC Implementation

**Events:**
- `InitializeCamera` - Initialize camera and face detection
- `StartCountdown` - Begin 3-2-1 countdown
- `StartRecording` - Begin video recording
- `StopRecording` - End recording and validate
- `ProcessFrame` - Handle real-time face detection
- `ResetCapture` - Reset to initial state

**States:**
- `Initial` - Initial state
- `CameraInitializing` - Setting up camera
- `CameraReady` - Ready to start recording
- `CountdownInProgress` - 3-2-1 countdown active
- `Recording` - Video recording in progress
- `Processing` - Validating recorded video
- `Success` - Recording successful with good face coverage
- `Failure` - Recording failed or poor face coverage
- `Error` - Camera or detection error

### Phase 3: Core Services

#### 3.1 Face Detection Service
```dart
class FaceDetectionService {
  // Initialize ML Kit face detector
  // Process camera frames for face detection
  // Calculate face coverage percentage
  // Return real-time feedback
}
```

#### 3.2 Camera Service
```dart
class CameraService {
  // Initialize front camera
  // Configure video recording settings
  // Handle camera permissions
  // Manage camera lifecycle
}
```

#### 3.3 Video Validation Service
```dart
class VideoValidationService {
  // Analyze face coverage throughout recording
  // Calculate success rate (frames with ≥80% coverage)
  // Generate validation report
}
```

### Phase 4: UI Components

#### 4.1 Main Page Structure
- `FaceVideoCapturePage` - Main container with BLoC provider
- Responsive layout using existing `responsive_framework`
- Dark theme consistency with `AppColors`
- Proper error handling and loading states

#### 4.2 Camera Preview Widget
- Full-screen camera preview
- Overlay face guide (translucent oval/circle)
- Real-time feedback border (red/green)
- Recording indicator

#### 4.3 Control Widgets
- Start recording button (following existing button styles)
- Countdown timer display (3-2-1)
- Recording progress indicator
- Success/failure result screen

### Phase 5: Integration Points

#### 5.1 Dependency Injection
```dart
// Add to lib/core/di/injection.dart
void _registerFaceVerificationDependencies() {
  getIt.registerLazySingleton<FaceDetectionService>(
    () => FaceDetectionService(),
  );
  getIt.registerLazySingleton<VideoStorageRepository>(
    () => VideoStorageRepository(),
  );
  getIt.registerFactory<FaceVideoCaptureBloc>(
    () => FaceVideoCaptureBloc(
      faceDetectionService: getIt<FaceDetectionService>(),
      videoStorageRepository: getIt<VideoStorageRepository>(),
    ),
  );
}
```

#### 5.2 Router Integration
```dart
// Add to lib/core/router/app_router.dart
GoRoute(
  path: '/face-verification',
  name: 'face-verification',
  builder: (context, state) => const FaceVideoCapturePage(),
),
```

#### 5.3 Logging Integration
- Use existing `LoggerService` with structured format
- Module constant: `LoggingConstants.faceVerificationModule`
- Log format: `'[FACE_VERIFICATION] Action: Details'`

### Phase 6: Testing Strategy

#### 6.1 Unit Tests
- `FaceDetectionService` logic
- `VideoValidationService` calculations
- BLoC state transitions
- Face coverage percentage calculations

#### 6.2 Widget Tests
- Camera preview widget
- Countdown timer functionality
- UI state changes
- Error handling

#### 6.3 Integration Tests
- End-to-end camera workflow
- Permission handling
- Video recording and validation
- Cross-platform compatibility

### Phase 7: Future Integration Placeholders

#### 7.1 Firebase Integration
```dart
// Placeholder for video upload
class VideoUploadService {
  // TODO: Implement Firebase Storage upload
  // TODO: Add video metadata (face coverage stats)
  // TODO: Generate upload progress feedback
}
```

#### 7.2 Backend API Integration
```dart
// Placeholder for verification API
class VerificationApiService {
  // TODO: Send video to backend for processing
  // TODO: Receive verification results
  // TODO: Handle API errors and retries
}
```

## 🔧 Implementation Constraints

### Code Quality Requirements
- Must pass `flutter analyze` with 0 issues
- Follow existing BLoC architecture patterns
- Maintain UI/UX consistency with current design system
- Implement proper error handling for camera/ML Kit failures
- Ensure proper resource cleanup (camera, detectors)

### Performance Considerations
- Optimize face detection frame processing
- Manage memory usage during video recording
- Handle device rotation and app lifecycle
- Implement proper camera resource management

### Platform Compatibility
- Test on both Android and iOS devices
- Handle different camera resolutions
- Manage platform-specific permissions
- Test in various lighting conditions

## 📝 Deliverables Checklist

- [ ] Feature branch: `feature/face-verification-video` ✅
- [ ] Implementation plan: `face_video_capture_todo.md` ✅
- [ ] Dependencies added and configured
- [ ] Core BLoC architecture implemented
- [ ] Face detection service with ML Kit
- [ ] Camera service with camerawesome
- [ ] UI components with face guide overlay
- [ ] Real-time feedback system
- [ ] 9-second recording with auto-stop
- [ ] Video validation logic
- [ ] Success/failure screens
- [ ] Unit tests for core logic
- [ ] Integration tests for camera workflow
- [ ] Documentation updates
- [ ] `docs/timeline.md` updated with implementation details

## 🚀 Next Steps

1. **Phase 1**: Add dependencies and configure permissions
2. **Phase 2**: Implement core BLoC architecture
3. **Phase 3**: Develop face detection and camera services
4. **Phase 4**: Build UI components and integrate
5. **Phase 5**: Add dependency injection and routing
6. **Phase 6**: Implement comprehensive testing
7. **Phase 7**: Add future integration placeholders
8. **Final**: Update documentation and timeline

---

**Note**: This implementation follows the existing project patterns for BLoC state management, dependency injection with GetIt, structured logging, and Material Design 3 dark theme consistency.
