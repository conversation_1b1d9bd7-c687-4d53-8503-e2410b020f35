import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template face_detection_repository}
/// Repository that manages face detection operations and coordinates
/// between the face detection service and the BLoC layer.
/// {@endtemplate}
class FaceDetectionRepository {
  /// {@macro face_detection_repository}
  FaceDetectionRepository({
    FaceDetectionService? faceDetectionService,
  }) : _faceDetectionService = faceDetectionService ?? FaceDetectionService();

  final FaceDetectionService _faceDetectionService;
  final LoggerService _logger = LoggerService();

  bool _isInitialized = false;

  /// Whether the repository is initialized and ready for use
  bool get isInitialized => _isInitialized;

  /// Initializes the face detection repository and underlying services
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection repository already initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection repository initialization started',
      ),
    );

    try {
      await _faceDetectionService.initialize();
      _isInitialized = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection repository initialization completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection repository initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Detects faces in the current camera frame
  /// 
  /// Returns a [FaceDetectionResult] with detection information,
  /// or null if detection is not available or fails.
  Future<FaceDetectionResult?> detectFace() async {
    if (!_isInitialized) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection attempted before initialization',
        ),
      );
      return null;
    }

    try {
      return await _faceDetectionService.detectFaceInCurrentFrame();
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection frame processing failed',
          'Error: $error',
        ),
      );
      return null;
    }
  }

  /// Validates face coverage for a specific frame
  /// 
  /// Returns true if the face coverage meets the minimum threshold.
  Future<bool> validateFaceCoverage(FaceDetectionResult result) async {
    if (!_isInitialized) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face coverage validation attempted before initialization',
        ),
      );
      return false;
    }

    try {
      return _faceDetectionService.validateCoverage(result);
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face coverage validation failed',
          'Error: $error',
        ),
      );
      return false;
    }
  }

  /// Gets the current face detection configuration
  Map<String, dynamic> getDetectionConfig() {
    return _faceDetectionService.getConfiguration();
  }

  /// Updates the face detection configuration
  Future<void> updateDetectionConfig(Map<String, dynamic> config) async {
    if (!_isInitialized) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Detection config update attempted before initialization',
        ),
      );
      return;
    }

    try {
      await _faceDetectionService.updateConfiguration(config);
      
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection configuration updated',
          'Config: $config',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection configuration update failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Disposes of the repository and releases resources
  Future<void> dispose() async {
    if (!_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection repository disposal skipped - not initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection repository disposal started',
      ),
    );

    try {
      await _faceDetectionService.dispose();
      _isInitialized = false;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection repository disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection repository disposal error: $error',
        ),
        error,
        stackTrace,
      );
      // Don't rethrow disposal errors
    }
  }
}
