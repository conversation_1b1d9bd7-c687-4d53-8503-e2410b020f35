import 'package:equatable/equatable.dart';

/// {@template video_capture_config}
/// Configuration settings for video capture during face verification.
/// {@endtemplate}
class VideoCaptureConfig extends Equatable {
  /// {@macro video_capture_config}
  const VideoCaptureConfig({
    this.recordingDuration = const Duration(seconds: 9),
    this.countdownDuration = const Duration(seconds: 3),
    this.faceDetectionInterval = const Duration(milliseconds: 100),
    this.minimumFaceCoverage = 80.0,
    this.videoQuality = VideoQuality.high,
    this.enableAudio = true,
    this.cameraLens = CameraLens.front,
  });

  /// Duration of the video recording (default: 9 seconds)
  final Duration recordingDuration;

  /// Duration of the countdown before recording starts (default: 3 seconds)
  final Duration countdownDuration;

  /// Interval between face detection checks (default: 100ms)
  final Duration faceDetectionInterval;

  /// Minimum face coverage percentage required (default: 80%)
  final double minimumFaceCoverage;

  /// Video quality setting
  final VideoQuality videoQuality;

  /// Whether to record audio with the video
  final bool enableAudio;

  /// Which camera lens to use
  final CameraLens cameraLens;

  @override
  List<Object> get props => [
        recordingDuration,
        countdownDuration,
        faceDetectionInterval,
        minimumFaceCoverage,
        videoQuality,
        enableAudio,
        cameraLens,
      ];

  @override
  String toString() {
    return 'VideoCaptureConfig('
        'recordingDuration: ${recordingDuration.inSeconds}s, '
        'countdownDuration: ${countdownDuration.inSeconds}s, '
        'faceDetectionInterval: ${faceDetectionInterval.inMilliseconds}ms, '
        'minimumFaceCoverage: $minimumFaceCoverage%, '
        'videoQuality: $videoQuality, '
        'enableAudio: $enableAudio, '
        'cameraLens: $cameraLens'
        ')';
  }
}

/// {@template video_quality}
/// Enumeration of available video quality settings.
/// {@endtemplate}
enum VideoQuality {
  /// Low quality video (480p)
  low,

  /// Medium quality video (720p)
  medium,

  /// High quality video (1080p)
  high,

  /// Ultra high quality video (4K)
  ultraHigh;

  /// Gets the resolution description for this quality setting
  String get description {
    switch (this) {
      case VideoQuality.low:
        return '480p';
      case VideoQuality.medium:
        return '720p';
      case VideoQuality.high:
        return '1080p';
      case VideoQuality.ultraHigh:
        return '4K';
    }
  }

  /// Gets the approximate bitrate for this quality setting
  int get bitrate {
    switch (this) {
      case VideoQuality.low:
        return 1000000; // 1 Mbps
      case VideoQuality.medium:
        return 3000000; // 3 Mbps
      case VideoQuality.high:
        return 8000000; // 8 Mbps
      case VideoQuality.ultraHigh:
        return 20000000; // 20 Mbps
    }
  }
}

/// {@template camera_lens}
/// Enumeration of available camera lenses.
/// {@endtemplate}
enum CameraLens {
  /// Front-facing camera (selfie camera)
  front,

  /// Back-facing camera (main camera)
  back;

  /// Gets the display name for this camera lens
  String get displayName {
    switch (this) {
      case CameraLens.front:
        return 'Front Camera';
      case CameraLens.back:
        return 'Back Camera';
    }
  }
}
