import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'face_video_capture_event.dart';
part 'face_video_capture_state.dart';

/// {@template face_video_capture_bloc}
/// BLoC that manages the face verification video capture process.
///
/// Handles camera initialization, countdown, recording, face detection,
/// and video validation according to the specified requirements.
/// {@endtemplate}
class FaceVideoCaptureBloc
    extends Bloc<FaceVideoCaptureEvent, FaceVideoCaptureState> {
  /// {@macro face_video_capture_bloc}
  FaceVideoCaptureBloc({
    required FaceDetectionRepository faceDetectionRepository,
    required VideoStorageRepository videoStorageRepository,
    VideoCaptureConfig? config,
  })  : _faceDetectionRepository = faceDetectionRepository,
        _videoStorageRepository = videoStorageRepository,
        _config = config ?? const VideoCaptureConfig(),
        super(const Initial()) {
    // Register event handlers
    on<InitializeCamera>(_onInitializeCamera);
    on<StartCountdown>(_onStartCountdown);
    on<CountdownTick>(_onCountdownTick);
    on<StartRecording>(_onStartRecording);
    on<StopRecording>(_onStopRecording);
    on<ProcessFrame>(_onProcessFrame);
    on<RecordingProgress>(_onRecordingProgress);
    on<ResetCapture>(_onResetCapture);
    on<DisposeResources>(_onDisposeResources);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'BLoC initialized',
        'Config: $_config',
      ),
    );
  }

  final FaceDetectionRepository _faceDetectionRepository;
  final VideoStorageRepository _videoStorageRepository;
  final VideoCaptureConfig _config;
  final LoggerService _logger = LoggerService();

  // Timers for countdown and recording
  Timer? _countdownTimer;
  Timer? _recordingTimer;
  Timer? _faceDetectionTimer;

  // Recording state tracking
  DateTime? _recordingStartTime;
  final List<FaceDetectionResult> _detectionResults = [];

  @override
  Future<void> close() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'BLoC closing',
        'Disposing timers and resources',
      ),
    );

    // Cancel all timers
    _countdownTimer?.cancel();
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    // Dispose repositories
    await _faceDetectionRepository.dispose();
    await _videoStorageRepository.dispose();

    return super.close();
  }

  /// Handles camera initialization
  Future<void> _onInitializeCamera(
    InitializeCamera event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera initialization started',
      ),
    );

    emit(CameraInitializing(config: _config));

    try {
      // Initialize face detection
      await _faceDetectionRepository.initialize();

      // Initialize video storage
      await _videoStorageRepository.initialize();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Camera initialization completed',
        ),
      );

      emit(CameraReady(config: _config));
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Camera initialization failed: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        Error(error: 'Failed to initialize camera: $error', config: _config),
      );
    }
  }

  /// Handles countdown start
  Future<void> _onStartCountdown(
    StartCountdown event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Countdown started',
        'Duration: ${_config.countdownDuration.inSeconds}s',
      ),
    );

    var remainingSeconds = _config.countdownDuration.inSeconds;
    emit(
      CountdownInProgress(
        remainingSeconds: remainingSeconds,
        config: _config,
        currentDetection: state.currentDetection,
        coverageStats: state.coverageStats,
      ),
    );

    _countdownTimer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        remainingSeconds--;
        if (remainingSeconds > 0) {
          add(CountdownTick(remainingSeconds));
        } else {
          timer.cancel();
          add(const StartRecording());
        }
      },
    );
  }

  /// Handles countdown tick
  void _onCountdownTick(
    CountdownTick event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    emit(
      CountdownInProgress(
        remainingSeconds: event.remainingSeconds,
        config: _config,
        currentDetection: state.currentDetection,
        coverageStats: state.coverageStats,
      ),
    );
  }

  /// Handles recording start
  Future<void> _onStartRecording(
    StartRecording event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording started',
        'Duration: ${_config.recordingDuration.inSeconds}s',
      ),
    );

    try {
      // Start video recording
      await _videoStorageRepository.startRecording();

      _recordingStartTime = DateTime.now();
      _detectionResults.clear();

      // Start face detection timer
      _startFaceDetection();

      // Start recording progress timer
      _startRecordingTimer();

      emit(
        Recording(
          elapsedTime: Duration.zero,
          remainingTime: _config.recordingDuration,
          config: _config,
          currentDetection: state.currentDetection,
          coverageStats: state.coverageStats,
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Failed to start recording: $error',
        ),
        error,
        stackTrace,
      );

      emit(Error(error: 'Failed to start recording: $error', config: _config));
    }
  }

  /// Starts the face detection timer
  void _startFaceDetection() {
    _faceDetectionTimer = Timer.periodic(
      _config.faceDetectionInterval,
      (timer) async {
        try {
          final result = await _faceDetectionRepository.detectFace();
          if (result != null && !isClosed) {
            add(ProcessFrame(result));
          }
        } catch (error) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Face detection frame skipped',
              'Error: $error',
            ),
          );
        }
      },
    );
  }

  /// Starts the recording progress timer
  void _startRecordingTimer() {
    _recordingTimer = Timer.periodic(
      const Duration(milliseconds: 100),
      (timer) {
        if (_recordingStartTime == null) return;

        final elapsed = DateTime.now().difference(_recordingStartTime!);
        final remaining = _config.recordingDuration - elapsed;

        if (remaining.inMilliseconds <= 0) {
          timer.cancel();
          add(const StopRecording());
        } else {
          add(RecordingProgress(elapsed, remaining));
        }
      },
    );
  }

  /// Handles frame processing for face detection
  void _onProcessFrame(
    ProcessFrame event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    _detectionResults.add(event.detectionResult);

    // Update current state with new detection
    if (state is Recording) {
      final recordingState = state as Recording;
      emit(
        Recording(
          elapsedTime: recordingState.elapsedTime,
          remainingTime: recordingState.remainingTime,
          config: _config,
          currentDetection: event.detectionResult,
          coverageStats: _calculateCoverageStats(),
        ),
      );
    }
  }

  /// Handles recording progress updates
  void _onRecordingProgress(
    RecordingProgress event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    if (state is Recording) {
      emit(
        Recording(
          elapsedTime: event.elapsedTime,
          remainingTime: event.remainingTime,
          config: _config,
          currentDetection: state.currentDetection,
          coverageStats: _calculateCoverageStats(),
        ),
      );
    }
  }

  /// Handles recording stop and validation
  Future<void> _onStopRecording(
    StopRecording event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording stopped',
        'Processing ${_detectionResults.length} detection results',
      ),
    );

    // Cancel timers
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    emit(
      Processing(
        config: _config,
        currentDetection: state.currentDetection,
        coverageStats: _calculateCoverageStats(),
      ),
    );

    try {
      // Stop video recording and get file path
      final videoPath = await _videoStorageRepository.stopRecording();

      // Calculate final coverage statistics
      final finalStats = _calculateCoverageStats();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Recording validation completed',
          'Quality score: ${finalStats.qualityScore.toStringAsFixed(1)}, '
              'Meets threshold: ${finalStats.meetsQualityThreshold}',
        ),
      );

      // Determine success or failure based on quality threshold
      if (finalStats.meetsQualityThreshold) {
        emit(
          Success(
            videoPath: videoPath,
            config: _config,
            currentDetection: state.currentDetection,
            coverageStats: finalStats,
          ),
        );
      } else {
        emit(
          Failure(
            reason: 'Insufficient face coverage. '
                'Quality score: ${finalStats.qualityScore.toStringAsFixed(1)}% '
                '(minimum required: 70%)',
            videoPath: videoPath,
            config: _config,
            currentDetection: state.currentDetection,
            coverageStats: finalStats,
          ),
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Recording validation failed: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        Error(error: 'Failed to process recording: $error', config: _config),
      );
    }
  }

  /// Handles capture reset
  void _onResetCapture(
    ResetCapture event,
    Emitter<FaceVideoCaptureState> emit,
  ) {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Capture reset',
      ),
    );

    // Cancel all timers
    _countdownTimer?.cancel();
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    // Clear detection results
    _detectionResults.clear();
    _recordingStartTime = null;

    emit(CameraReady(config: _config));
  }

  /// Handles resource disposal
  Future<void> _onDisposeResources(
    DisposeResources event,
    Emitter<FaceVideoCaptureState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Resources disposal started',
      ),
    );

    // Cancel all timers
    _countdownTimer?.cancel();
    _recordingTimer?.cancel();
    _faceDetectionTimer?.cancel();

    try {
      // Dispose repositories
      await _faceDetectionRepository.dispose();
      await _videoStorageRepository.dispose();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Resources disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Resource disposal error: $error',
        ),
        error,
        stackTrace,
      );
    }

    emit(const Initial());
  }

  /// Calculates coverage statistics from detection results
  FaceCoverageStats _calculateCoverageStats() {
    if (_detectionResults.isEmpty) {
      return const FaceCoverageStats.empty();
    }

    final totalFrames = _detectionResults.length;
    final framesWithFace =
        _detectionResults.where((result) => result.faceDetected).length;
    final framesWithValidCoverage =
        _detectionResults.where((result) => result.meetsThreshold).length;

    final coverageValues =
        _detectionResults.map((result) => result.coveragePercentage).toList();

    final averageCoverage = coverageValues.isNotEmpty
        ? coverageValues.reduce((a, b) => a + b) / coverageValues.length
        : 0.0;

    final minimumCoverage = coverageValues.isNotEmpty
        ? coverageValues.reduce((a, b) => a < b ? a : b)
        : 0.0;

    final maximumCoverage = coverageValues.isNotEmpty
        ? coverageValues.reduce((a, b) => a > b ? a : b)
        : 0.0;

    final recordingDuration = _recordingStartTime != null
        ? DateTime.now().difference(_recordingStartTime!)
        : Duration.zero;

    return FaceCoverageStats(
      totalFrames: totalFrames,
      framesWithFace: framesWithFace,
      framesWithValidCoverage: framesWithValidCoverage,
      averageCoverage: averageCoverage,
      minimumCoverage: minimumCoverage,
      maximumCoverage: maximumCoverage,
      recordingDuration: recordingDuration,
      detectionResults: List.from(_detectionResults),
    );
  }
}
