import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:flutter/material.dart';

/// {@template face_guide_overlay}
/// Widget that displays a face guide overlay with real-time detection feedback.
///
/// Shows an oval guide for face positioning and changes color based on
/// face detection quality and coverage.
/// {@endtemplate}
class FaceGuideOverlay extends StatefulWidget {
  /// {@macro face_guide_overlay}
  const FaceGuideOverlay({
    super.key,
    this.currentDetection,
    this.isRecording = false,
  });

  /// Current face detection result
  final FaceDetectionResult? currentDetection;

  /// Whether recording is in progress
  final bool isRecording;

  @override
  State<FaceGuideOverlay> createState() => _FaceGuideOverlayState();
}

class _FaceGuideOverlayState extends State<FaceGuideOverlay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _borderController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _borderAnimation;

  @override
  void initState() {
    super.initState();

    // Pulse animation for guide
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ),
    );

    // Border animation for feedback
    _borderController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _borderAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _borderController,
        curve: Curves.easeInOut,
      ),
    );

    _pulseController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(FaceGuideOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Animate border when detection changes
    if (widget.currentDetection != oldWidget.currentDetection) {
      _borderController.forward().then((_) {
        if (mounted) {
          _borderController.reverse();
        }
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _borderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            // Dark overlay with cutout
            _buildDarkOverlay(constraints),

            // Face guide
            _buildFaceGuide(constraints),

            // Detection feedback
            if (widget.currentDetection != null)
              _buildDetectionFeedback(constraints),

            // Instructions
            _buildInstructions(constraints),
          ],
        );
      },
    );
  }

  /// Builds the dark overlay with face guide cutout
  Widget _buildDarkOverlay(BoxConstraints constraints) {
    return CustomPaint(
      size: Size(constraints.maxWidth, constraints.maxHeight),
      painter: FaceGuideOverlayPainter(
        guideColor: _getGuideColor(),
        pulseValue: _pulseAnimation.value,
        isRecording: widget.isRecording,
      ),
    );
  }

  /// Builds the face guide oval
  Widget _buildFaceGuide(BoxConstraints constraints) {
    final guideWidth = constraints.maxWidth * 0.7;
    final guideHeight = constraints.maxHeight * 0.5;

    return Positioned(
      left: (constraints.maxWidth - guideWidth) / 2,
      top: (constraints.maxHeight - guideHeight) / 2,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Container(
              width: guideWidth,
              height: guideHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.elliptical(
                    guideWidth / 2,
                    guideHeight / 2,
                  ),
                ),
                border: Border.all(
                  color: _getGuideColor(),
                  width: 3,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Builds detection feedback indicators
  Widget _buildDetectionFeedback(BoxConstraints constraints) {
    final detection = widget.currentDetection!;

    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: AnimatedBuilder(
        animation: _borderAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: 0.8 + (0.2 * _borderAnimation.value),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: _getGuideColor().withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _getFeedbackMessage(detection),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Coverage: ${detection.coveragePercentage.toStringAsFixed(0)}%',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Builds instruction text
  Widget _buildInstructions(BoxConstraints constraints) {
    if (widget.isRecording) {
      return const SizedBox.shrink(); // Hide instructions during recording
    }

    return Positioned(
      bottom: 200,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'Position your face within the oval guide.\n'
          'Keep your face centered and well-lit.',
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Gets the guide color based on detection quality
  Color _getGuideColor() {
    if (widget.currentDetection == null) {
      return Colors.white; // Default color when no detection
    }

    final detection = widget.currentDetection!;

    if (!detection.faceDetected) {
      return Colors.red; // No face detected
    }

    if (detection.faceCount > 1) {
      return Colors.orange; // Multiple faces detected
    }

    if (detection.meetsThreshold) {
      return Colors.green; // Good coverage
    }

    if (detection.coveragePercentage > 60) {
      return Colors.yellow; // Moderate coverage
    }

    return Colors.red; // Poor coverage
  }

  /// Gets feedback message based on detection result
  String _getFeedbackMessage(FaceDetectionResult detection) {
    if (!detection.faceDetected) {
      return 'No face detected';
    }

    if (detection.faceCount > 1) {
      return 'Multiple faces detected';
    }

    if (detection.meetsThreshold) {
      return 'Perfect! Keep this position';
    }

    if (detection.coveragePercentage > 60) {
      return 'Move closer to the camera';
    }

    return 'Position your face in the guide';
  }
}

/// {@template face_guide_overlay_painter}
/// Custom painter for the face guide overlay with cutout effect.
/// {@endtemplate}
class FaceGuideOverlayPainter extends CustomPainter {
  /// {@macro face_guide_overlay_painter}
  const FaceGuideOverlayPainter({
    required this.guideColor,
    required this.pulseValue,
    required this.isRecording,
  });

  /// Color of the guide border
  final Color guideColor;

  /// Pulse animation value (0.0 to 1.0)
  final double pulseValue;

  /// Whether recording is in progress
  final bool isRecording;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Draw dark overlay
    paint.color = Colors.black.withValues(alpha: 0.5);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

    // Create cutout for face guide
    final guideWidth = size.width * 0.7;
    final guideHeight = size.height * 0.5;
    final guideRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: guideWidth * pulseValue,
      height: guideHeight * pulseValue,
    );

    // Cut out the guide area
    paint.blendMode = BlendMode.clear;
    canvas.drawOval(guideRect, paint);

    // Draw guide border with glow effect if recording
    paint.blendMode = BlendMode.srcOver;
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = isRecording ? 4.0 : 3.0;
    paint.color = guideColor;

    if (isRecording) {
      // Add glow effect during recording
      paint.maskFilter = const MaskFilter.blur(BlurStyle.outer, 3);
    }

    canvas.drawOval(guideRect, paint);
  }

  @override
  bool shouldRepaint(covariant FaceGuideOverlayPainter oldDelegate) {
    return oldDelegate.guideColor != guideColor ||
        oldDelegate.pulseValue != pulseValue ||
        oldDelegate.isRecording != isRecording;
  }
}
